<?php

declare(strict_types=1);

namespace App\GraphQL\Queries;

use App\GraphQL\Types\WorkflowDefinitionType;
use App\Models\WorkflowDefinition;
use GraphQL\Type\Definition\ResolveInfo;
use GraphQL\Type\Definition\Type;
use Rebing\GraphQL\Support\Facades\GraphQL;
use Rebing\GraphQL\Support\Query;
use Rebing\GraphQL\Support\SelectFields;
use Closure;
/**
 * GraphQL Query for fetching `workflow_definitions` records.
 */
class WorkflowDefinitionInfoQuery extends Query
{
	protected $attributes = [
	    'name' => 'workflowDefinitionInfoQuery',
        'description' => 'workflow info '
    ];


	public function type(): Type
	{

		return GraphQL::type('WorkflowDefinitionType');
	}


	public function args(): array
	{
		return [
		            'id' => [
		                'type' => Type::nonNull(Type::id()),
		                'description' => '工作流ID',
		            ],
		        ];
	}

    public function resolve($root, array $args)
    {
        return WorkflowDefinition::findOrFail($args['id']);
    }
}
