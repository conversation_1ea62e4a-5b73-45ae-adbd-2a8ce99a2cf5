<?php

declare(strict_types=1);

namespace App\GraphQL\Queries;

use App\GraphQL\Types\AdminMenuType;
use Dcat\Admin\Models\Menu as AdminMenu;
use GraphQL\Type\Definition\ResolveInfo;
use GraphQL\Type\Definition\Type;
use Rebing\GraphQL\Support\Facades\GraphQL;
use Rebing\GraphQL\Support\Query;
use Rebing\GraphQL\Support\SelectFields;
use Closure;
use App\Models\AdminUser;
use App\Exceptions\GraphQLException;

/**
 * GraphQL Query for fetching `admin_menu` records.
 */
class AdminMenuQuery extends AdminBaseQuery
{
	protected $attributes = ['name' => 'AdminMenuQuery', 'description' => '获取菜单'];


	public function type(): Type
	{
		return Type::listOf(GraphQL::type('AdminMenuType'));
	}

	//
	public function args(): array
	{
		return [
            'parent_id' => [
                'type' => Type::int(),
                'description' => '父级ID',
            ],
        ];
	}


	public function resolve($root, array $args, $context, ResolveInfo $info, SelectFields $fields): mixed
	{
		$parent_id = !empty($args['parent_id']) ? $args['parent_id']:0;
	    $menus = auth()->guard('adminapi')->user()->getMenus($parent_id);
        return collect($menus)->toArray();

		try {
			$user = auth('admin')->user();
			
			if (!$user) {
				throw new GraphQLException('Unauthorized access', 401, [
					'code' => 'UNAUTHORIZED'
				]);
			}

			if (!$user instanceof AdminUser) {
				throw new GraphQLException('Invalid user type', 403, [
					'code' => 'INVALID_USER_TYPE'
				]);
			}

			$parent_id = $args['parent_id'] ?? 0;
			return $user->getMenus($parent_id);

		} catch (GraphQLException $e) {
			throw $e;
		} catch (\Exception $e) {
			throw new GraphQLException(
				'Failed to fetch menu data',
				500,
				[
					'code' => 'INTERNAL_ERROR',
					'internal_message' => $e->getMessage()
				]
			);
		}
	}
}
