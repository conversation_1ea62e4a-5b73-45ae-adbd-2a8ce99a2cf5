<?php

declare(strict_types=1);

namespace App\GraphQL\Types;

use App\Models\WorkflowDefinition;
use GraphQL\Type\Definition\Type;
use Rebing\GraphQL\Support\Type as GraphQLType;

class WorkflowDefinitionType extends GraphQLType
{
	protected $attributes = [
		'name' => 'WorkflowDefinitionType',
		'description' => '工作流定义类型',
		'model' => WorkflowDefinition::class
	];

	public function fields(): array
	{
		return [
			'id' => [
				'type' => Type::id(),
				'description' => '工作流ID'
			],
			'name' => [
				'type' => Type::string(),
				'description' => '工作流名称'
			],
			'code' => [
				'type' => Type::string(),
				'description' => '工作流唯一标识码'
			],
			'description' => [
				'type' => Type::string(),
				'description' => '工作流描述'
			],
			'form_type' => [
				'type' => Type::string(),
				'description' => '表单类型：custom-自定义表单, table-数据表单'
			],
			'form_id' => [
				'type' => Type::string(),
				'description' => '关联表单ID或表名'
			],
			'node_config' => [
				'type' => Type::string(),
				'description' => '流程节点配置'
			],
			'flow_permissions' => [
				'type' => Type::string(),
				'description' => '流程权限配置'
			],
			'enable_timing' => [
				'type' => Type::string(),
				'description' => '是否启用定时器'
			],
			'timing_hours' => [
				'type' => Type::string(),
				'description' => '定时器小时数'
			],
			'enable_batch' => [
				'type' => Type::string(),
				'description' => '是否允许批量审批'
			],
			'enable_mobile' => [
				'type' => Type::string(),
				'description' => '是否支持移动端'
			],
			'status' => [
				'type' => Type::string(),
				'description' => '状态：0-禁用，1-启用'
			],
			'sort' => [
				'type' => Type::int(),
				'description' => '排序'
			],
			'department_id' => [
				'type' => Type::string(),
				'description' => '所属部门ID'
			],
			'creator_id' => [
				'type' => Type::string(),
				'description' => '创建人ID'
			],
			'created_at' => [
				'type' => Type::string(),
				'description' => '创建时间'
			],
			'updated_at' => [
				'type' => Type::string(),
				'description' => '更新时间'
			],
			'deleted_at' => [
				'type' => Type::string(),
				'description' => '删除时间'
			]
		];
	}
}
