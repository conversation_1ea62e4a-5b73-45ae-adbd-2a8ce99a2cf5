<?php

declare(strict_types=1);

namespace App\GraphQL\Queries;

use App\Models\WorkflowDefinition;
use GraphQL\Type\Definition\ResolveInfo;
use GraphQL\Type\Definition\Type;
use Rebing\GraphQL\Support\Facades\GraphQL;
use Rebing\GraphQL\Support\Query;
use Rebing\GraphQL\Support\SelectFields;
use Illuminate\Pagination\LengthAwarePaginator;
use App\GraphQL\Types\WorkflowDefinitionPaginationType;

/**
 * GraphQL Query for fetching `workflow_definitions` records.
 */
class WorkflowDefinitionListQuery extends Query {
    protected $attributes = [
        'name'        => 'workflowDefinitionListQuery',
        'description' => 'workflow list with pagination, filtering and sorting'
    ];

    public function type(): Type {
        return GraphQL::type('WorkflowDefinitionPaginationType');
    }

    public function args(): array {
        return [
            'limit' => [
                'type' => Type::int(),
                'defaultValue' => 10,
                'description' => '每页记录数',
            ],
            'page' => [
                'type' => Type::int(),
                'defaultValue' => 1,
                'description' => '当前页码',
            ],
            'id' => [
                'type' => Type::id(),
                'description' => '工作流ID',
            ],
            'name' => [
                'type' => Type::string(),
                'description' => '工作流名称',
            ],
            'code' => [
                'type' => Type::string(),
                'description' => '工作流唯一标识码',
            ],
            'description' => [
                'type' => Type::string(),
                'description' => '工作流描述',
            ],
            'form_type' => [
                'type' => Type::string(),
                'description' => '表单类型：custom-自定义表单, table-数据表单',
            ],
            'form_id' => [
                'type' => Type::string(),
                'description' => '关联表单ID或表名',
            ],
            'enable_timing' => [
                'type' => Type::string(),
                'description' => '是否启用定时器',
            ],
            'enable_batch' => [
                'type' => Type::string(),
                'description' => '是否允许批量审批',
            ],
            'enable_mobile' => [
                'type' => Type::string(),
                'description' => '是否支持移动端',
            ],
            'status' => [
                'type' => Type::string(),
                'description' => '状态：0-禁用，1-启用',
            ],
            'department_id' => [
                'type' => Type::string(),
                'description' => '所属部门ID',
            ],
            'creator_id' => [
                'type' => Type::string(),
                'description' => '创建人ID',
            ],
            'sort_field' => [
                'type' => Type::string(),
                'defaultValue' => 'created_at',
                'description' => '排序字段',
            ],
            'sort_order' => [
                'type' => Type::string(),
                'defaultValue' => 'desc',
                'description' => '排序方向：asc-升序，desc-降序',
            ],
        ];
    }

    public function resolve($root, array $args, $context, ResolveInfo $info, SelectFields $fields): mixed {
        $query = WorkflowDefinition::query();

        // 添加条件筛选
        if (isset($args['id'])) {
            $query->where('id', $args['id']);
        }
        if (isset($args['name'])) {
            $query->where('name', 'like', '%' . $args['name'] . '%');
        }
        if (isset($args['code'])) {
            $query->where('code', $args['code']);
        }
        if (isset($args['form_type'])) {
            $query->where('form_type', $args['form_type']);
        }
        if (isset($args['form_id'])) {
            $query->where('form_id', $args['form_id']);
        }
        if (isset($args['enable_timing'])) {
            $query->where('enable_timing', $args['enable_timing']);
        }
        if (isset($args['enable_batch'])) {
            $query->where('enable_batch', $args['enable_batch']);
        }
        if (isset($args['enable_mobile'])) {
            $query->where('enable_mobile', $args['enable_mobile']);
        }
        if (isset($args['status'])) {
            $query->where('status', $args['status']);
        }
        if (isset($args['department_id'])) {
            $query->where('department_id', $args['department_id']);
        }
        if (isset($args['creator_id'])) {
            $query->where('creator_id', $args['creator_id']);
        }

        // 添加排序
        $sortField = $args['sort_field'] ?? 'created_at';
        $sortOrder = $args['sort_order'] ?? 'desc';
        $query->orderBy($sortField, $sortOrder);

        // 获取分页参数
        $limit = $args['limit'] ?? 10;
        $page = $args['page'] ?? 1;

        // 执行分页查询
        $paginator = $query->paginate($limit, ['*'], 'page', $page);

        // 返回分页数据
        return [
            'total' => $paginator->total(),
            'per_page' => $paginator->perPage(),
            'current_page' => $paginator->currentPage(),
            'last_page' => $paginator->lastPage(),
            'has_more_pages' => $paginator->hasMorePages(),
            'data' => $paginator->items(),
        ];
    }
}
