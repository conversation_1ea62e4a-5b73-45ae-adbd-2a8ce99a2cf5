<?php

declare(strict_types=1);

namespace App\GraphQL\Mutations;

use App\GraphQL\Types\AdminMenuType;
use App\Models\AdminMenu as ModelClass;
use Closure;
use GraphQL\Type\Definition\ResolveInfo;
use GraphQL\Type\Definition\Type;
use Illuminate\Support\Facades\Validator;
use Rebing\GraphQL\Support\Facades\GraphQL;
use Rebing\GraphQL\Support\Mutation;

/**
 * GraphQL Mutation for updating `admin_menu` records.
 */
class AdminMenuMutation extends Mutation
{
	protected $attributes = ['name' => 'AdminMenuMutation', 'description' => ''];


	public function type(): Type
	{
		return GraphQL::type('AdminMenuType');
	}


	public function args(): array
	{
		return [
		            'id' => [
		                'type' => Type::id(),
		                'description' => '',
		            ],
		            'parent_id' => [
		                'type' => Type::nonNull(Type::string()),
		                'description' => '',
		            ],
		            'order' => [
		                'type' => Type::nonNull(Type::string()),
		                'description' => '',
		            ],
		            'title' => [
		                'type' => Type::nonNull(Type::string()),
		                'description' => '',
		            ],
		            'icon' => [
		                'type' => Type::string(),
		                'description' => '',
		            ],
		            'uri' => [
		                'type' => Type::string(),
		                'description' => '',
		            ],
		            'extension' => [
		                'type' => Type::nonNull(Type::string()),
		                'description' => '',
		            ],
		            'show' => [
		                'type' => Type::nonNull(Type::string()),
		                'description' => '',
                        'defaultValue'=> 1,
		            ],
		            'created_at' => [
		                'type' => Type::string(),
		                'description' => '',
		            ],
		            'updated_at' => [
		                'type' => Type::string(),
		                'description' => '',
		            ],
		        ];
	}


	protected function rules(array $args = []): array
	{
		return [
		            'id' => ['required', 'exists:admin_menus,id'],
		            'parent_id' => ['required', 'integer'],
		            'order' => ['required', 'integer'],
		            'title' => ['required', 'string'],
		            'icon' => ['nullable', 'string'],
		            'uri' => ['nullable', 'string'],
		            'extension' => ['required', 'string'],
		            'show' => ['required'],
		            'created_at' => ['nullable'],
		            'updated_at' => ['nullable'],
		        ];
	}


	public function resolve($root, array $args, $context, ResolveInfo $info, Closure $fields): mixed
	{
		// 查找记录
		    $adminMenu = ModelClass::find($args['id']);
		    if (!$adminMenu) {
		        throw new \Exception('Record not found.');
		    }

		    // 更新字段
		        if (isset($args['parent_id'])) {
		            $adminMenu->parent_id = $args['parent_id'];
		        }
		        if (isset($args['order'])) {
		            $adminMenu->order = $args['order'];
		        }
		        if (isset($args['title'])) {
		            $adminMenu->title = $args['title'];
		        }
		        if (isset($args['icon'])) {
		            $adminMenu->icon = $args['icon'];
		        }
		        if (isset($args['uri'])) {
		            $adminMenu->uri = $args['uri'];
		        }
		        if (isset($args['extension'])) {
		            $adminMenu->extension = $args['extension'];
		        }
		        if (isset($args['show'])) {
		            $adminMenu->show = $args['show'];
		        }
		        if (isset($args['created_at'])) {
		            $adminMenu->created_at = $args['created_at'];
		        }
		        if (isset($args['updated_at'])) {
		            $adminMenu->updated_at = $args['updated_at'];
		        }

		        $adminMenu->save();

		        return $adminMenu;
	}
}
