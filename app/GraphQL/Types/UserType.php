<?php
namespace App\GraphQL\Types;

use App\Models\User;
use GraphQL\Type\Definition\Type;
use Rebing\GraphQL\Support\Type as GraphQLType;

class UserType extends GraphQLType
{
    protected $attributes = [
        'name' => 'User',
        'description' => '用户信息字段',
        'model' => User::class
    ];

    public function fields(): array
    {
        return [
            'id' => [
                'type' => Type::nonNull(Type::int()),
                'description' => '用户ID'
            ],
            'name' => [
                'type' => Type::string(),
                'description' => '用户名称'
            ],
            'email' => [
                'type' => Type::string(),
                'description' => '邮箱'
            ],
            'created_at' => [
                'type' => Type::string(),
                'description' => '创建时间'
            ]
        ];
    }
}