<?php

declare(strict_types=1);

namespace App\GraphQL\Schemas;

use Rebing\GraphQL\Support\Contracts\ConfigConvertible;

/**
 * Default GraphQL Schema.
 */
class DefaultSchema implements ConfigConvertible
{
	public function toConfig(): array
	{
		return [
		    'query' => [
\App\GraphQL\Queries\WorkflowDefinitionQuery::class,
            SkuAttributeQuery::class,
            AdminMenuQuery::class,
            DepartmentQuery::class,
        ],
		    'mutation' => [
		        // 添加 Mutation 类
		    ],
		    'types' => [
		        // 添加 Type 类
		    ],
		];
	}
}
