<?php

declare(strict_types=1);

namespace App\GraphQL\Queries;

use App\GraphQL\Types\DepartmentType;
use App\Models\Department;
use GraphQL\Type\Definition\ResolveInfo;
use GraphQL\Type\Definition\Type;
use Rebing\GraphQL\Support\Facades\GraphQL;
use Rebing\GraphQL\Support\Query;
use Rebing\GraphQL\Support\SelectFields;

/**
 * GraphQL Query for fetching `departments` records.
 */
class DepartmentQuery extends Query
{
	protected $attributes = ['name' => 'departmentQuery', 'description' => '部门管理表'];


	public function type(): Type
	{
		return Type::listOf(GraphQL::type('DepartmentType'));
	}


	public function args(): array
	{
		return [
		            'id' => [
		                'type' => Type::nonNull(Type::id()),
		                'description' => '',
		            ],
		            'department_key' => [
		                'type' => Type::nonNull(Type::string()),
		                'description' => '部门唯一标识',
		            ],
		            'department_name' => [
		                'type' => Type::nonNull(Type::string()),
		                'description' => '部门名称',
		            ],
		            'department_names' => [
		                'type' => Type::nonNull(Type::string()),
		                'description' => '部门全称（包含父级）',
		            ],
		            'parent_id' => [
		                'type' => Type::nonNull(Type::string()),
		                'description' => '父部门ID',
		            ],
		            'sort' => [
		                'type' => Type::nonNull(Type::string()),
		                'description' => '排序',
		            ],
		            'status' => [
		                'type' => Type::nonNull(Type::string()),
		                'description' => '状态：0-禁用，1-启用',
		            ],
		            'created_at' => [
		                'type' => Type::string(),
		                'description' => '',
		            ],
		            'updated_at' => [
		                'type' => Type::string(),
		                'description' => '',
		            ],
		            'deleted_at' => [
		                'type' => Type::string(),
		                'description' => '',
		            ],
		        ];
	}


	public function resolve($root, array $args, $context, ResolveInfo $info, SelectFields $fields): mixed
	{
		return Department::all();
	}
}
