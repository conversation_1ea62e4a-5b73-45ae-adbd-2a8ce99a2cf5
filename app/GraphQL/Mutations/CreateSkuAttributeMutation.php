<?php

declare(strict_types=1);

namespace App\GraphQL\Mutations;

use App\GraphQL\Types\SkuAttributeType;
use Dcat\Admin\Models\SkuAttribute as ModelClass;
use Closure;
use GraphQL\Type\Definition\ResolveInfo;
use GraphQL\Type\Definition\Type;
use Illuminate\Support\Facades\Validator;
use Rebing\GraphQL\Support\Facades\GraphQL;
use Rebing\GraphQL\Support\Mutation;

/**
 * GraphQL Mutation for updating `sku_attribute` records.
 */
class CreateSkuAttributeMutation extends Mutation
{
	protected $attributes = ['name' => 'skuAttributeMutation', 'description' => ''];


	public function type(): Type
	{
		return GraphQL::type('SkuAttributeType');
	}


	public function args(): array
	{
		return [
		            'id' => [
		                'type' => Type::id(),
		                'description' => '',
		            ],
		            'app_name' => [
		                'type' => Type::string(),
		                'description' => '',
		            ],
		            'admin_id' => [
		                'type' => Type::string(),
		                'description' => '',
		            ],
		            'attr_name' => [
		                'type' => Type::nonNull(Type::string()),
		                'description' => '规格名称',
		            ],
                    'attr_type' => [
                        'type' => GraphQL::type('AttributeType'),
                        'description' => '规格类型 (checkbox 或 radio)',
                    ],
		            'attr_value' => [
		                'type' => Type::string(),
		                'description' => '规格值',
		            ],
		            'sort' => [
		                'type' => Type::nonNull(Type::string()),
		                'description' => '排序',
		            ],
		            'created_at' => [
		                'type' => Type::string(),
		                'description' => '',
		            ],
		            'updated_at' => [
		                'type' => Type::string(),
		                'description' => '',
		            ],
		        ];
	}


	protected function rules(array $args = []): array
	{
		return [
		            'id' => ['required', 'exists:sku_attributes,id'],
		            'app_name' => ['nullable', 'string'],
		            'admin_id' => ['nullable', 'integer'],
		            'attr_name' => ['required', 'string'],
		            'attr_type' => ['required'],
		            'attr_value' => ['nullable'],
		            'sort' => ['required'],
		            'created_at' => ['nullable'],
		            'updated_at' => ['nullable'],
		        ];
	}

	protected function messages(): array
	{
		return [
			'id.required' => 'ID不能为空',
			'id.exists' => '该记录不存在',
			'app_name.string' => '应用名称必须是字符串',
			'admin_id.integer' => '管理员ID必须是整数',
			'attr_name.required' => '规格名称不能为空',
			'attr_name.string' => '规格名称必须是字符串',
			'attr_type.required' => '规格类型不能为空',
			'sort.required' => '排序值不能为空',
		];
	}


	public function resolve($root, array $args, $context, ResolveInfo $info, Closure $fields): mixed
	{
        return ModelClass::create($args);
	}
}
