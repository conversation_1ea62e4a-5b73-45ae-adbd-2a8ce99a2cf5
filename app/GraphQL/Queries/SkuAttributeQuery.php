<?php

declare(strict_types=1);

namespace App\GraphQL\Queries;

//use App\GraphQL\Types\PaginationType;
use Dcat\Admin\Models\SkuAttribute;
use GraphQL\Type\Definition\ResolveInfo;
use GraphQL\Type\Definition\Type;
use Rebing\GraphQL\Support\Facades\GraphQL;
use Rebing\GraphQL\Support\Query;
use Rebing\GraphQL\Support\SelectFields;
use App\GraphQL\Types\PaginationType;

/**
 * GraphQL Query for fetching `sku_attribute` records.
 */
class SkuAttributeQuery extends Query
{
	protected $attributes = ['name' => 'skuAttributeQuery', 'description' => ''];


	public function type(): Type
	{
        return GraphQL::type('PaginationType');
	}


	public function args(): array
	{
		return [
		            'id' => [
		                'type' => Type::id(),
		                'description' => '',
		            ],
		            'app_name' => [
		                'type' => Type::string(),
		                'description' => '',
		            ],
		            'admin_id' => [
		                'type' => Type::string(),
		                'description' => '',
		            ],
		            'attr_name' => [
		                'type' => Type::string(),
		                'description' => '规格名称',
		            ],
		            'attr_type' => [
		                'type' => Type::string(),
		                'description' => '规格类型',
		            ],
		            'attr_value' => [
		                'type' => Type::string(),
		                'description' => '规格值',
		            ],
		            'sort' => [
		                'type' => Type::string(),
		                'description' => '排序',
		            ],
		            'created_at' => [
		                'type' => Type::string(),
		                'description' => '',
		            ],
		            'updated_at' => [
		                'type' => Type::string(),
		                'description' => '',
		            ],
		            'limit' => [
		                'type' => Type::int(),
		                'description' => '每页记录数',
		            ],
		            'page' => [
		                'type' => Type::int(),
		                'description' => '当前页码',
		            ],
		        ];
	}


	public function resolve($root, array $args, $context, ResolveInfo $info, SelectFields $fields): mixed
	{
		$query = SkuAttribute::query();
		
		// 获取分页参数
		$limit = $args['limit'] ?? 10;
		$page = $args['page'] ?? 1;

		// 执行分页查询
		$paginator = $query->paginate($limit, ['*'], 'page', $page);

		// 返回分页数据
		return [
			'total' => $paginator->total(),
			'per_page' => $paginator->perPage(),
			'current_page' => $paginator->currentPage(),
			'last_page' => $paginator->lastPage(),
			'has_more_pages' => $paginator->hasMorePages(),
			'data' => $paginator->items(),
		];
	}
}
