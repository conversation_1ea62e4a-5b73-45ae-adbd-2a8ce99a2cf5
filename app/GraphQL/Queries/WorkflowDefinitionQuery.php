<?php

declare(strict_types=1);

namespace App\GraphQL\Queries;

use App\Models\WorkflowDefinition;
use GraphQL\Type\Definition\Type;
use Rebing\GraphQL\Support\Facades\GraphQL;
use Rebing\GraphQL\Support\Query;

class WorkflowDefinitionQuery extends Query
{
    protected $attributes = [
        'name' => 'workflowDefinitionQuery',
        'description' => '获取工作流定义详情'
    ];

    public function type(): Type
    {
        //return Type::listOf(GraphQL::type('WorkflowDefinitionType'));
        return GraphQL::type('WorkflowDefinitionType');
    }

    public function args(): array
    {
        return [
            'id' => [
                'type' => Type::nonNull(Type::id()),
                'description' => '工作流定义ID'
            ]
        ];
    }

    public function resolve($root, array $args)
    {
        return WorkflowDefinition::findOrFail($args['id']);
    }
} 