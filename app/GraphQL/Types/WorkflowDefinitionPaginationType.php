<?php

declare(strict_types=1);

namespace App\GraphQL\Types;

use GraphQL\Type\Definition\Type;
use Rebing\GraphQL\Support\Facades\GraphQL;
use Rebing\GraphQL\Support\Type as GraphQLType;

class WorkflowDefinitionPaginationType extends GraphQLType
{
    protected $attributes = [
        'name' => 'WorkflowDefinitionPaginationType',
        'description' => '工作流定义分页数据'
    ];

    public function fields(): array
    {
        return [
            'total' => [
                'type' => Type::int(),
                'description' => '总记录数'
            ],
            'per_page' => [
                'type' => Type::int(),
                'description' => '每页记录数'
            ],
            'current_page' => [
                'type' => Type::int(),
                'description' => '当前页码'
            ],
            'last_page' => [
                'type' => Type::int(),
                'description' => '最后一页页码'
            ],
            'has_more_pages' => [
                'type' => Type::boolean(),
                'description' => '是否有更多页'
            ],
            'data' => [
                'type' => Type::listOf(GraphQL::type('WorkflowDefinitionType')),
                'description' => '分页数据'
            ]
        ];
    }
} 