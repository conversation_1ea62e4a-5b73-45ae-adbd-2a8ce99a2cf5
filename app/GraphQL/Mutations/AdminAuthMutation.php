<?php

namespace App\GraphQL\Mutations;

use App\Models\AdminUser;
use GraphQL\Type\Definition\Type;
use Rebing\GraphQL\Support\Mutation;
use Rebing\GraphQL\Support\Facades\GraphQL;
use Illuminate\Support\Facades\Auth;

class AdminAuthMutation extends Mutation
{
    protected $attributes = [
        'name' => 'login'
    ];

    public function type(): Type
    {
        return GraphQL::type('AdminAuthType');
    }

    public function args(): array
    {
        return [
            'username' => [
                'name' => 'username',
                'type' => Type::nonNull(Type::string()),
            ],
            'password' => [
                'name' => 'password',
                'type' => Type::nonNull(Type::string()),
            ]
        ];
    }

    public function resolve($root, array $args)
    {
        $credentials = [
            'username' => $args['username'],
            'password' => $args['password'],
        ];

        if (!$token = Auth::guard('adminapi')->attempt($credentials)) {
            throw new \Exception('Invalid credentials');
        }

        $user = Auth::guard('adminapi')->user();

        return [
            'id'=> $user->id,
            'username' => $user->username,
            'avatar' => $user->avatar,
            'token' => $token,
        ];
    }
} 